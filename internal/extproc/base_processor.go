// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package extproc

import (
	"context"
	"fmt"
	"log/slog"

	"github.com/envoyproxy/ai-gateway/filterapi"
	"github.com/envoyproxy/ai-gateway/internal/extproc/backendauth"
)

// baseProcessorConfig holds common configuration for all processors.
type baseProcessorConfig struct {
	config         *processorConfig
	requestHeaders map[string]string
	logger         *slog.Logger
}

// newBaseProcessorConfig creates a new baseProcessorConfig.
func newBaseProcessorConfig(config *processorConfig, requestHeaders map[string]string, logger *slog.Logger, processorType string) *baseProcessorConfig {
	return &baseProcessorConfig{
		config:         config,
		requestHeaders: requestHeaders,
		logger:         logger.With("processor", processorType),
	}
}

// validateAPISchema validates that the API schema is supported.
func (b *baseProcessorConfig) validateAPISchema() error {
	if b.config.schema.Name != filterapi.APISchemaOpenAI {
		return fmt.Errorf("unsupported API schema: %s", b.config.schema.Name)
	}
	return nil
}

// baseUpstreamProcessor holds common fields for upstream processors.
type baseUpstreamProcessor struct {
	*baseProcessorConfig
	responseHeaders   map[string]string
	responseEncoding  string
	modelNameOverride string
	handler           backendauth.Handler
	onRetry           bool
}

// newBaseUpstreamProcessor creates a new baseUpstreamProcessor.
func newBaseUpstreamProcessor(config *processorConfig, requestHeaders map[string]string, logger *slog.Logger, processorType string, onRetry bool) *baseUpstreamProcessor {
	return &baseUpstreamProcessor{
		baseProcessorConfig: newBaseProcessorConfig(config, requestHeaders, logger, processorType),
		onRetry:             onRetry,
	}
}

// setupBackendAuth sets up backend authentication if configured.
func (b *baseUpstreamProcessor) setupBackendAuth(ctx context.Context, backend *filterapi.Backend) error {
	if backend.Auth != nil {
		handler, err := backendauth.NewHandler(ctx, backend.Auth)
		if err != nil {
			return fmt.Errorf("failed to create auth handler: %w", err)
		}
		b.handler = handler
	}
	return nil
}

// ProcessRequestHeaders handles common request header processing logic.
func (b *baseUpstreamProcessor) processRequestHeadersCommon(ctx context.Context) error {
	// Extract response encoding from headers
	if encoding := b.requestHeaders["accept-encoding"]; encoding != "" {
		b.responseEncoding = encoding
	}

	return nil
}

// ProcessResponseHeaders handles common response header processing logic.
func (b *baseUpstreamProcessor) processResponseHeadersCommon(ctx context.Context, headers map[string]string) error {
	b.responseHeaders = headers

	// Extract content encoding from response headers
	if encoding := headers["content-encoding"]; encoding != "" {
		b.responseEncoding = encoding
	}

	return nil
}
