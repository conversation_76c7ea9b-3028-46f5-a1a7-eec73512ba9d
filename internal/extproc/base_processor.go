// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package extproc

import (
	"context"
	"fmt"
	"log/slog"

	corev3 "github.com/envoyproxy/go-control-plane/envoy/config/core/v3"
	extprocv3 "github.com/envoyproxy/go-control-plane/envoy/service/ext_proc/v3"

	"github.com/envoyproxy/ai-gateway/filterapi"
	"github.com/envoyproxy/ai-gateway/internal/extproc/backendauth"
)

type processorType string

const (
	processorTypeChatCompletion processorType = "chat-completion"
	processorTypeEmbedding      processorType = "embedding"
)

// BaseMetrics defines the common interface for all AI operation metrics.
type BaseMetrics interface {
	StartRequest(map[string]string)
	SetModel(string)
	SetBackend(*filterapi.Backend)
	RecordRequestCompletion(context.Context, bool)
}

// BaseRouterFilter provides common functionality for router filters.
type BaseRouterFilter struct {
	passThroughProcessor
	config                 *processorConfig
	requestHeaders         map[string]string
	logger                 *slog.Logger
	originalRequestBody    any
	originalRequestBodyRaw []byte
	upstreamFilter         Processor
	upstreamFilterCount    int
}

// NewBaseRouterFilter creates a new BaseRouterFilter.
func NewBaseRouterFilter(config *processorConfig, requestHeaders map[string]string, logger *slog.Logger) BaseRouterFilter {
	return BaseRouterFilter{
		config:         config,
		requestHeaders: requestHeaders,
		logger:         logger,
	}
}

// ProcessRequestBodyCommon handles common request body processing logic.
func (b *BaseRouterFilter) ProcessRequestBodyCommon(_ context.Context, rawBody *extprocv3.HttpBody, parseBodyFunc func(*extprocv3.HttpBody) (string, interface{}, error)) (*extprocv3.ProcessingResponse, error) {
	model, body, err := parseBodyFunc(rawBody)
	if err != nil {
		return nil, fmt.Errorf("failed to parse request body: %w", err)
	}

	b.originalRequestBody = body
	b.originalRequestBodyRaw = rawBody.Body

	b.requestHeaders[b.config.modelNameHeaderKey] = model
	routeName, err := b.config.router.Calculate(b.requestHeaders)
	if err != nil {
		return nil, fmt.Errorf("failed to route request: %w", err)
	}

	b.requestHeaders[b.config.selectedRouteHeaderKey] = string(routeName)
	return &extprocv3.ProcessingResponse{
		Response: &extprocv3.ProcessingResponse_RequestBody{
			RequestBody: &extprocv3.BodyResponse{
				Response: &extprocv3.CommonResponse{
					Status: extprocv3.CommonResponse_CONTINUE,
				},
			},
		},
	}, nil
}

// BaseUpstreamFilter provides common functionality for upstream filters.
type BaseUpstreamFilter struct {
	config           *processorConfig
	requestHeaders   map[string]string
	responseHeaders  map[string]string
	responseEncoding string
	handler          backendauth.Handler
	logger           *slog.Logger
	metrics          BaseMetrics
}

// NewBaseUpstreamFilter creates a new BaseUpstreamFilter.
func NewBaseUpstreamFilter(config *processorConfig, requestHeaders map[string]string, logger *slog.Logger, metrics BaseMetrics) *BaseUpstreamFilter {
	return &BaseUpstreamFilter{
		config:         config,
		requestHeaders: requestHeaders,
		logger:         logger,
		metrics:        metrics,
	}
}

// ProcessRequestHeadersCommon handles common request header processing logic.
func (b *BaseUpstreamFilter) ProcessRequestHeadersCommon(ctx context.Context, translateFunc func() (*extprocv3.HeaderMutation, *extprocv3.BodyMutation, error)) (*extprocv3.ProcessingResponse, error) {
	var err error
	defer func() {
		if err != nil {
			b.metrics.RecordRequestCompletion(ctx, false)
		}
	}()

	// Start tracking metrics for this request.
	b.metrics.StartRequest(b.requestHeaders)
	b.metrics.SetModel(b.requestHeaders[b.config.modelNameHeaderKey])

	headerMutation, bodyMutation, err := translateFunc()
	if err != nil {
		return nil, fmt.Errorf("failed to transform request: %w", err)
	}

	if headerMutation == nil {
		headerMutation = &extprocv3.HeaderMutation{}
	} else {
		for _, h := range headerMutation.SetHeaders {
			b.requestHeaders[h.Header.Key] = string(h.Header.RawValue)
		}
	}

	if h := b.handler; h != nil {
		if err = h.Do(ctx, b.requestHeaders, headerMutation, bodyMutation); err != nil {
			return nil, fmt.Errorf("failed to do auth request: %w", err)
		}
	}

	return &extprocv3.ProcessingResponse{
		Response: &extprocv3.ProcessingResponse_RequestHeaders{
			RequestHeaders: &extprocv3.HeadersResponse{
				Response: &extprocv3.CommonResponse{
					Status:         extprocv3.CommonResponse_CONTINUE_AND_REPLACE,
					HeaderMutation: headerMutation,
					BodyMutation:   bodyMutation,
				},
			},
		},
	}, nil
}

// ProcessResponseHeadersCommon handles common response header processing logic.
func (b *BaseUpstreamFilter) ProcessResponseHeadersCommon(_ context.Context, headers *corev3.HeaderMap) (*extprocv3.ProcessingResponse, error) {
	b.responseHeaders = make(map[string]string)
	for _, h := range headers.Headers {
		b.responseHeaders[h.Key] = string(h.RawValue)
	}

	// Extract content encoding from response headers
	if encoding := b.responseHeaders["content-encoding"]; encoding != "" {
		b.responseEncoding = encoding
	}

	return &extprocv3.ProcessingResponse{
		Response: &extprocv3.ProcessingResponse_ResponseHeaders{
			ResponseHeaders: &extprocv3.HeadersResponse{
				Response: &extprocv3.CommonResponse{
					Status: extprocv3.CommonResponse_CONTINUE,
				},
			},
		},
	}, nil
}

// SetBackendCommon handles common backend setup logic.
func (b *BaseUpstreamFilter) SetBackendCommon(ctx context.Context, backend *filterapi.Backend, backendHandler backendauth.Handler, _ Processor, selectTranslatorFunc func(filterapi.VersionedAPISchema) error, incrementCountFunc func()) error {
	var err error
	defer func() {
		b.metrics.RecordRequestCompletion(ctx, err == nil)
	}()

	incrementCountFunc()
	b.metrics.SetBackend(backend)

	if err = selectTranslatorFunc(backend.Schema); err != nil {
		return fmt.Errorf("failed to select translator: %w", err)
	}

	b.handler = backendHandler
	return nil
}

// createProcessorFactory creates a processor factory function with common validation.
func createProcessorFactory(processorType processorType, createRouterFilter func(*processorConfig, map[string]string, *slog.Logger) Processor, createUpstreamFilter func(*processorConfig, map[string]string, *slog.Logger) Processor) ProcessorFactory {
	return func(config *processorConfig, requestHeaders map[string]string, logger *slog.Logger, isUpstreamFilter bool) (Processor, error) {
		if config.schema.Name != filterapi.APISchemaOpenAI {
			return nil, fmt.Errorf("unsupported API schema: %s", config.schema.Name)
		}

		logger = logger.With("processor", processorType, "isUpstreamFilter", fmt.Sprintf("%v", isUpstreamFilter))

		if !isUpstreamFilter {
			return createRouterFilter(config, requestHeaders, logger), nil
		}

		return createUpstreamFilter(config, requestHeaders, logger), nil
	}
}
